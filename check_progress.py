#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查图片识别进度和结果
"""

import json
import os
from datetime import datetime

def check_progress():
    """检查处理进度"""
    temp_file = "temp_qwen_zhipu_comparison_results.json"
    final_file = "qwen_zhipu_comparison_results.json"
    
    # 检查临时文件
    if os.path.exists(temp_file):
        try:
            with open(temp_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"当前进度: 已处理 {len(results)} 张图片")
            
            # 统计成功率
            qwen_success = sum(1 for r in results if r.get("qwen_result", {}).get("success", False))
            zhipu_success = sum(1 for r in results if r.get("zhipu_result", {}).get("success", False))
            
            print(f"千问成功: {qwen_success}/{len(results)} ({qwen_success/len(results)*100:.1f}%)")
            print(f"智谱成功: {zhipu_success}/{len(results)} ({zhipu_success/len(results)*100:.1f}%)")
            
            # 显示最近处理的几张图片
            print("\n最近处理的图片:")
            for i, result in enumerate(results[-3:], len(results)-2):
                print(f"{i}. {result['image_name']} - {result['timestamp']}")
                if result.get("qwen_result", {}).get("success"):
                    content = result["qwen_result"]["content"][:50] + "..." if len(result["qwen_result"]["content"]) > 50 else result["qwen_result"]["content"]
                    print(f"   千问: {content}")
                else:
                    print(f"   千问: 失败 - {result.get('qwen_result', {}).get('error', '未知错误')}")
                
                if result.get("zhipu_result", {}).get("success"):
                    content = result["zhipu_result"]["content"][:50] + "..." if len(result["zhipu_result"]["content"]) > 50 else result["zhipu_result"]["content"]
                    print(f"   智谱: {content}")
                else:
                    print(f"   智谱: 失败 - {result.get('zhipu_result', {}).get('error', '未知错误')}")
                print()
                
        except Exception as e:
            print(f"读取临时文件失败: {e}")
    
    # 检查最终文件
    elif os.path.exists(final_file):
        try:
            with open(final_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"处理完成! 总共处理了 {len(results)} 张图片")
            
            # 统计成功率
            qwen_success = sum(1 for r in results if r.get("qwen_result", {}).get("success", False))
            zhipu_success = sum(1 for r in results if r.get("zhipu_result", {}).get("success", False))
            
            print(f"千问成功: {qwen_success}/{len(results)} ({qwen_success/len(results)*100:.1f}%)")
            print(f"智谱成功: {zhipu_success}/{len(results)} ({zhipu_success/len(results)*100:.1f}%)")
            
        except Exception as e:
            print(f"读取最终文件失败: {e}")
    
    else:
        print("未找到结果文件，处理可能尚未开始或文件被删除")

if __name__ == "__main__":
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    check_progress()
