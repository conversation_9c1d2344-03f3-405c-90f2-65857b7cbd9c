#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片作文内容识别对比工具
使用千问和智谱两个模型对图片进行作文内容识别
"""

import os
import base64
import json
import time
from pathlib import Path
from typing import List, Dict, Any
import requests
from openai import OpenAI
from zhipuai import ZhipuAI

class ImageRecognitionComparison:
    def __init__(self):
        # API配置
        self.qwen_api_key = "sk-747a3bf32b8943ff96595e35450041de"
        self.zhipu_api_key = "57548585b39d43e9bd36abcb35d8cbf4.66lZ4t2SGbRHLLDM"
        
        # 初始化客户端
        self.qwen_client = OpenAI(
            api_key=self.qwen_api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        self.zhipu_client = ZhipuAI(api_key=self.zhipu_api_key)
        
        # 提示词
        self.prompt = "请识别图中的作文内容，不要输出任何和作文无关的内容，如果是作文题目，请完整输出作文题目内容"
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64格式"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"编码图片失败 {image_path}: {e}")
            return None
    
    def call_qwen_api(self, image_path: str) -> Dict[str, Any]:
        """调用千问API进行图片识别"""
        try:
            # 编码图片
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                return {"error": "图片编码失败"}
            
            # 构建请求
            completion = self.qwen_client.chat.completions.create(
                model="qwen-vl-plus",
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                        {"type": "text", "text": self.prompt}
                    ]
                }]
            )
            
            result = {
                "model": "qwen-vl-plus",
                "content": completion.choices[0].message.content,
                "usage": completion.usage.dict() if completion.usage else None,
                "success": True
            }
            
            return result
            
        except Exception as e:
            return {
                "model": "qwen-vl-plus",
                "error": str(e),
                "success": False
            }
    
    def call_zhipu_api(self, image_path: str) -> Dict[str, Any]:
        """调用智谱API进行图片识别"""
        try:
            # 编码图片
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                return {"error": "图片编码失败"}
            
            # 构建请求
            response = self.zhipu_client.chat.completions.create(
                model="glm-4.1v-thinking-flashx",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ]
            )
            
            result = {
                "model": "glm-4.1v-thinking-flashx",
                "content": response.choices[0].message.content,
                "usage": response.usage.dict() if hasattr(response, 'usage') and response.usage else None,
                "success": True
            }
            
            return result
            
        except Exception as e:
            return {
                "model": "glm-4.1v-thinking-flashx",
                "error": str(e),
                "success": False
            }
    
    def get_image_files(self, folder_path: str) -> List[str]:
        """获取文件夹中的所有图片文件"""
        image_files = []
        folder = Path(folder_path)
        
        if not folder.exists():
            print(f"文件夹不存在: {folder_path}")
            return image_files
        
        for file_path in folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                image_files.append(str(file_path))
        
        return sorted(image_files)
    
    def process_single_image(self, image_path: str) -> Dict[str, Any]:
        """处理单张图片"""
        print(f"正在处理图片: {image_path}")
        
        result = {
            "image_path": image_path,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "qwen_result": None,
            "zhipu_result": None
        }
        
        # 调用千问API
        print("  调用千问API...")
        result["qwen_result"] = self.call_qwen_api(image_path)
        time.sleep(1)  # 避免API调用过于频繁
        
        # 调用智谱API
        print("  调用智谱API...")
        result["zhipu_result"] = self.call_zhipu_api(image_path)
        time.sleep(1)  # 避免API调用过于频繁
        
        return result
    
    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """保存结果到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")
    
    def process_folder(self, folder_path: str, output_file: str = None):
        """处理整个文件夹的图片"""
        if output_file is None:
            output_file = f"recognition_results_{int(time.time())}.json"
        
        # 获取所有图片文件
        image_files = self.get_image_files(folder_path)
        
        if not image_files:
            print(f"在文件夹 {folder_path} 中没有找到支持的图片文件")
            return
        
        print(f"找到 {len(image_files)} 张图片，开始处理...")
        
        results = []
        
        for i, image_path in enumerate(image_files, 1):
            print(f"\n进度: {i}/{len(image_files)}")
            
            try:
                result = self.process_single_image(image_path)
                results.append(result)
                
                # 每处理10张图片保存一次结果（防止意外丢失）
                if i % 10 == 0:
                    temp_file = f"temp_{output_file}"
                    self.save_results(results, temp_file)
                    
            except Exception as e:
                print(f"处理图片失败 {image_path}: {e}")
                continue
        
        # 保存最终结果
        self.save_results(results, output_file)
        
        # 删除临时文件
        temp_file = f"temp_{output_file}"
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        print(f"\n处理完成！共处理 {len(results)} 张图片")
        return results

def main():
    """主函数"""
    # 创建识别器实例
    recognizer = ImageRecognitionComparison()
    
    # 设置图片文件夹路径
    folder_path = "202506（已标注）"
    
    # 设置输出文件名
    output_file = "qwen_zhipu_comparison_results.json"
    
    # 开始处理
    print("开始图片作文内容识别对比...")
    print(f"图片文件夹: {folder_path}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    results = recognizer.process_folder(folder_path, output_file)
    
    if results:
        print("\n处理统计:")
        qwen_success = sum(1 for r in results if r.get("qwen_result", {}).get("success", False))
        zhipu_success = sum(1 for r in results if r.get("zhipu_result", {}).get("success", False))
        
        print(f"千问成功: {qwen_success}/{len(results)}")
        print(f"智谱成功: {zhipu_success}/{len(results)}")

if __name__ == "__main__":
    main()
