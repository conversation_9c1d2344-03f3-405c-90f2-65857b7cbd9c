#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片作文内容识别对比工具
使用千问和智谱两个模型对图片进行作文内容识别
"""

import os
import base64
import json
import time
import glob
import requests

def encode_image_to_base64(image_path):
    """将图片编码为base64格式"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"编码图片失败 {image_path}: {e}")
        return None

def call_qwen_api(image_path, api_key):
    """调用千问API进行图片识别"""
    try:
        # 编码图片
        base64_image = encode_image_to_base64(image_path)
        if not base64_image:
            return {"error": "图片编码失败", "success": False}
        
        # 构建请求
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-vl-plus",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                    {"type": "text", "text": "请识别图中的作文内容，不要输出任何和作文无关的内容，如果是作文题目，请完整输出作文题目内容"}
                ]
            }]
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            return {
                "model": "qwen-vl-plus",
                "content": result["choices"][0]["message"]["content"],
                "usage": result.get("usage", {}),
                "success": True
            }
        else:
            return {
                "model": "qwen-vl-plus",
                "error": f"API调用失败: {response.status_code} - {response.text}",
                "success": False
            }
            
    except Exception as e:
        return {
            "model": "qwen-vl-plus",
            "error": str(e),
            "success": False
        }

def call_zhipu_api(image_path, api_key):
    """调用智谱API进行图片识别"""
    try:
        # 编码图片
        base64_image = encode_image_to_base64(image_path)
        if not base64_image:
            return {"error": "图片编码失败", "success": False}
        
        # 构建请求
        url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "glm-4v",
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "请识别图中的作文内容，不要输出任何和作文无关的内容，如果是作文题目，请完整输出作文题目内容"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                ]
            }]
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            return {
                "model": "glm-4v",
                "content": result["choices"][0]["message"]["content"],
                "usage": result.get("usage", {}),
                "success": True
            }
        else:
            return {
                "model": "glm-4v",
                "error": f"API调用失败: {response.status_code} - {response.text}",
                "success": False
            }
            
    except Exception as e:
        return {
            "model": "glm-4v",
            "error": str(e),
            "success": False
        }

def get_image_files(folder_path):
    """获取文件夹中的所有图片文件"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(folder_path, ext)
        image_files.extend(glob.glob(pattern))
        # 也搜索大写扩展名
        pattern = os.path.join(folder_path, ext.upper())
        image_files.extend(glob.glob(pattern))
    
    return sorted(list(set(image_files)))  # 去重并排序

def process_single_image(image_path, qwen_api_key, zhipu_api_key):
    """处理单张图片"""
    print(f"正在处理图片: {os.path.basename(image_path)}")
    
    result = {
        "image_path": image_path,
        "image_name": os.path.basename(image_path),
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "qwen_result": None,
        "zhipu_result": None
    }
    
    # 调用千问API
    print("  调用千问API...")
    result["qwen_result"] = call_qwen_api(image_path, qwen_api_key)
    time.sleep(2)  # 避免API调用过于频繁
    
    # 调用智谱API
    print("  调用智谱API...")
    result["zhipu_result"] = call_zhipu_api(image_path, zhipu_api_key)
    time.sleep(2)  # 避免API调用过于频繁
    
    return result

def save_results(results, output_file):
    """保存结果到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果失败: {e}")

def main():
    """主函数"""
    # API密钥
    qwen_api_key = "sk-747a3bf32b8943ff96595e35450041de"
    zhipu_api_key = "57548585b39d43e9bd36abcb35d8cbf4.66lZ4t2SGbRHLLDM"
    
    # 设置图片文件夹路径
    folder_path = "202506（已标注）"
    
    # 设置输出文件名
    output_file = "qwen_zhipu_comparison_results.json"
    
    print("开始图片作文内容识别对比...")
    print(f"图片文件夹: {folder_path}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        return
    
    # 获取所有图片文件
    image_files = get_image_files(folder_path)
    
    if not image_files:
        print(f"在文件夹 {folder_path} 中没有找到支持的图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片，开始处理...")
    
    results = []
    
    for i, image_path in enumerate(image_files, 1):
        print(f"\n进度: {i}/{len(image_files)}")
        
        try:
            result = process_single_image(image_path, qwen_api_key, zhipu_api_key)
            results.append(result)
            
            # 每处理5张图片保存一次结果（防止意外丢失）
            if i % 5 == 0:
                temp_file = f"temp_{output_file}"
                save_results(results, temp_file)
                
        except Exception as e:
            print(f"处理图片失败 {image_path}: {e}")
            continue
    
    # 保存最终结果
    save_results(results, output_file)
    
    # 删除临时文件
    temp_file = f"temp_{output_file}"
    if os.path.exists(temp_file):
        os.remove(temp_file)
    
    print(f"\n处理完成！共处理 {len(results)} 张图片")
    
    # 统计成功率
    if results:
        qwen_success = sum(1 for r in results if r.get("qwen_result", {}).get("success", False))
        zhipu_success = sum(1 for r in results if r.get("zhipu_result", {}).get("success", False))
        
        print("\n处理统计:")
        print(f"千问成功: {qwen_success}/{len(results)} ({qwen_success/len(results)*100:.1f}%)")
        print(f"智谱成功: {zhipu_success}/{len(results)} ({zhipu_success/len(results)*100:.1f}%)")

if __name__ == "__main__":
    main()
