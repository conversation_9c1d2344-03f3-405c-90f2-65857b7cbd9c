#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入模块
"""

try:
    print("Python version:")
    import sys
    print(sys.version)
    print("=" * 50)
    
    print("Testing OpenAI import...")
    from openai import OpenAI
    print("✓ OpenAI imported successfully")
    
    print("Testing ZhipuAI import...")
    from zhipuai import ZhipuAI
    print("✓ ZhipuAI imported successfully")
    
    print("Testing other imports...")
    import os
    import base64
    import json
    import time
    from pathlib import Path
    from typing import List, Dict, Any
    import requests
    print("✓ All other imports successful")
    
    print("\nAll imports successful!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
