import editdistance

class TextEvaluator:
    def __init__(self, ignore_blank=False, ignore_case=False):
        self.ignore_blank = ignore_blank
        self.ignore_case = ignore_case

    def preprocess(self, text):
        """文本预处理"""
        if self.ignore_blank:
            # 移除所有空白字符：空格、制表符、换行符等
            import re
            text = re.sub(r'\s+', '', text)
        if self.ignore_case:
            text = text.lower()
        return text

    def calculate_edit_distance(self, gt_text, pred_text):
        """计算编辑距离"""
        gt = self.preprocess(gt_text)
        pred = self.preprocess(pred_text)
        return editdistance.eval(gt, pred)

    def calculate_accuracy(self, gt_text, pred_text):
        """计算准确率，以答案（gt_text）的字数为标准"""
        gt = self.preprocess(gt_text)
        pred = self.preprocess(pred_text)

        # 计算编辑距离
        edit_dist = editdistance.eval(gt, pred)

        # 以答案的字数为标准计算准确率
        gt_length = len(gt)
        if gt_length == 0:
            return 1.0 if len(pred) == 0 else 0.0

        # 准确率 = (答案长度 - 编辑距离) / 答案长度
        accuracy = max(0, (gt_length - edit_dist) / gt_length)
        return accuracy

    def evaluate_text(self, gt_text, pred_text):
        """全面评估文本，返回详细指标"""
        gt = self.preprocess(gt_text)
        pred = self.preprocess(pred_text)

        edit_dist = editdistance.eval(gt, pred)
        gt_length = len(gt)
        pred_length = len(pred)

        # 计算准确率（以答案长度为标准）
        accuracy = self.calculate_accuracy(gt_text, pred_text)

        return {
            'edit_distance': edit_dist,
            'gt_length': gt_length,
            'pred_length': pred_length,
            'accuracy': accuracy,
            'gt_text': gt,
            'pred_text': pred
        }

def read_text_file(path):
    """读取文件内容（整个文件作为单个字符串）"""
    with open(path, "r", encoding="utf-8") as f:
        return f.read().strip()

if __name__ == "__main__":
    # 读取文件
    gt = read_text_file("../gt.txt")
    pred = read_text_file("../pred.txt")

    # 计算编辑距离
    evaluator = TextEvaluator(ignore_blank=False, ignore_case=False)
    #evaluator = TextEvaluator(ignore_blank=True, ignore_case=False)
    ed = evaluator.calculate_edit_distance(gt, pred)

    # 输出结果
    print(f"编辑距离: {ed}")